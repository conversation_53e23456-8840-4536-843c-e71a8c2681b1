from __future__ import annotations
import sublime
import sublime_plugin
import os
import time
import fnmatch
from collections import defaultdict, deque
from typing import Optional, Dict, Any, Set

# Import layout-centric classes
from .lib.layout_definition import LayoutDefinition, LayoutManager
from .lib.semantic_detector import SemanticTypeDetector
from .lib.layout_templates import LayoutTemplates
from .lib.project_integration import ProjectIntegration
from .lib.rule_engine import RuleEngine
from .lib.event_listener import AutoPlaceEventListener
from .lib.logging_utils import init_logger, get_logger

PLUGIN_NAME = "Jorn_AutoPlaceTabs"


class SettingsKeys:
    """Constants for settings keys."""
    AUTO_PLACE_ON_ACTIVATION = "auto_place_on_activation"
    AUTO_PLACE_ON_LOAD = "auto_place_on_load"
    ENABLE_DEBUG_PRINTS = "enable_debug_prints"
    EXCLUDE_PATTERNS = "exclude_patterns"
    MAX_PLACEMENTS_PER_SECOND = "max_placements_per_second"
    LARGE_FILE_THRESHOLD = "large_file_threshold"
    PROJECT_FILE_CHECK_INTERVAL = "project_file_check_interval"
    ACTIVE_LAYOUT = "active_layout"
    LAYOUTS = "layouts"


class Jorn_AutoPlaceTabsCommand(sublime_plugin.EventListener):
    """
    Main plugin that automatically places tabs in appropriate groups based on:
    - File type/extension patterns
    - Directory patterns  
    - Project membership
    - Semantic file types (unsaved, dirty, readonly, etc.)
    """
    
    _instance = None
    
    @classmethod
    def instance(cls):
        """Get the singleton instance of the plugin."""
        return cls._instance
    
    def __init__(self):
        super().__init__()
        
        # Initialize logger
        self.logger = init_logger(PLUGIN_NAME)
        
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.settings.add_on_change("jorn_auto_place_tabs", self._on_settings_changed)
        self._placement_locks = set()  # Per-view placement locks (view.id())
        self._placement_timestamps = defaultdict(deque)  # Per-window rate limiting
        self._last_placements = defaultdict(lambda: defaultdict(tuple))
        self._project_settings_cache = {}  # Cache project settings by window ID
        self._project_data_cache = {}  # Cache project data by window ID
        self._project_file_timestamps = {}  # Track project file modification times
        self._last_mtime_check = {}  # Track when we last checked mtime for each window
        self._file_access_errors = {}  # Track file access errors for adaptive intervals

        # Layout-centric components
        self.layout_manager = LayoutManager(self)
        self.semantic_detector = SemanticTypeDetector(self)
        self.project_integration = ProjectIntegration(self)
        self.rule_engine = RuleEngine(self)

        Jorn_AutoPlaceTabsCommand._instance = self
        
        # Set up event listener
        self._setup_event_listener()
    
    @classmethod
    def plugin_loaded(cls):
        """Called when the plugin is loaded."""
        if cls._instance is None:
            cls._instance = cls()
    
    @classmethod
    def plugin_unloaded(cls):
        """Called when the plugin is unloaded."""
        if cls._instance:
            cls._instance.settings.clear_on_change("jorn_auto_place_tabs")
            cls._instance = None
    
    def _debug_print(self, message: str) -> None:
        """Print debug message only if debug mode is enabled."""
        self.logger.set_debug_enabled(self.settings.get("enable_debug_prints", False))
        self.logger.debug(message)
    
    def _user_error(self, message: str, show_dialog: bool = False) -> None:
        """Show error to user via status bar or dialog."""
        self.logger.user_error(message, show_dialog)
    
    def _user_warning(self, message: str) -> None:
        """Show warning to user via status bar."""
        self.logger.user_warning(message)
    
    def _setup_event_listener(self) -> None:
        """Set up the event listener with plugin reference."""
        # The event listener is automatically registered by Sublime when the module loads
        # We just need to ensure it has a reference to this plugin instance
        pass  # Event listener will get plugin reference through instance() method
    
    def _on_settings_changed(self) -> None:
        """Handle settings changes."""
        self._debug_print("Settings changed, clearing cache")
        self._clear_settings_cache()
    
    def _get_setting(self, key: str, default: Any = None, window: Optional[sublime.Window] = None) -> Any:
        """Get setting value with project override support."""
        if window:
            project_settings = self._get_project_settings(window)
            if key in project_settings:
                return project_settings[key]
        
        return self.settings.get(key, default)
    
    def _get_project_settings(self, window: sublime.Window) -> Dict[str, Any]:
        """Get project-specific settings with caching."""
        window_id = window.id()
        
        # Check if project file has changed
        if self._has_project_file_changed(window):
            self._clear_settings_cache(window_id)
        
        # Return cached settings if available
        if window_id in self._project_settings_cache:
            return self._project_settings_cache[window_id]
        
        # Load project settings
        project_data = window.project_data()
        project_settings = {}
        
        if project_data and "settings" in project_data:
            plugin_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
            if isinstance(plugin_settings, dict):
                project_settings = plugin_settings
        
        # Cache the settings
        self._project_settings_cache[window_id] = project_settings
        return project_settings
    
    def _has_project_file_changed(self, window: sublime.Window) -> bool:
        """Check if project file has been modified since last check."""
        window_id = window.id()
        project_file_name = window.project_file_name()
        
        if not project_file_name:
            return False
        
        # Adaptive interval based on file access errors
        base_interval = self._get_setting("project_file_check_interval", 2.0, window)
        error_count = self._file_access_errors.get(window_id, 0)
        
        if error_count > 0:
            # Exponential backoff for problematic files/drives
            adaptive_interval = min(base_interval * (2 ** error_count), 30.0)  # Max 30 seconds
        else:
            adaptive_interval = base_interval
        
        # Check if enough time has passed since last check
        now = time.time()
        last_check = self._last_mtime_check.get(window_id, 0)
        if now - last_check < adaptive_interval:
            return False
        
        self._last_mtime_check[window_id] = now
        
        try:
            current_mtime = os.path.getmtime(project_file_name)
            cached_mtime = self._project_file_timestamps.get(window_id)
            
            if cached_mtime is None or current_mtime > cached_mtime:
                # Update timestamp and reset error count on success
                self._project_file_timestamps[window_id] = current_mtime
                if hasattr(self, '_file_access_errors'):
                    self._file_access_errors[window_id] = 0  # Reset error count on success
                self._debug_print(f"Project file changed detected: {project_file_name}")
                return cached_mtime is not None  # Don't invalidate on first check

            # Reset error count on successful access
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors[window_id] = 0
            return False
        except (OSError, IOError) as e:
            # File access error (network drive timeout, etc.) - track for adaptive intervals
            if not hasattr(self, '_file_access_errors'):
                self._file_access_errors = {}
            self._file_access_errors[window_id] = self._file_access_errors.get(window_id, 0) + 1
            
            error_msg = f"Project file access error (attempt {self._file_access_errors[window_id]}): {str(e)}"
            self._user_warning(error_msg)
            return True
    
    def _clear_settings_cache(self, window_id: Optional[int] = None) -> None:
        """Clear settings cache for a specific window or all windows."""
        if window_id:
            self._project_settings_cache.pop(window_id, None)
            self._project_data_cache.pop(window_id, None)
            self._project_file_timestamps.pop(window_id, None)
            self._last_mtime_check.pop(window_id, None)
            self._placement_timestamps.pop(window_id, None)
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.pop(window_id, None)
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.pop(window_id, None)
        else:
            self._project_settings_cache.clear()
            self._project_data_cache.clear()
            self._project_file_timestamps.clear()
            self._last_mtime_check.clear()
            self._placement_timestamps.clear()
            if hasattr(self, '_file_access_errors'):
                self._file_access_errors.clear()
            if hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning.clear()
    
    def _check_placement_frequency(self, window: sublime.Window) -> bool:
        """Check if placement frequency is within limits."""
        window_id = window.id()
        max_per_second = self._get_setting("max_placements_per_second", 20, window)
        
        now = time.time()
        timestamps = self._placement_timestamps[window_id]
        
        # Remove timestamps older than 1 second
        while timestamps and now - timestamps[0] > 1.0:
            timestamps.popleft()
        
        if len(timestamps) >= max_per_second:
            # Rate limit exceeded
            if not hasattr(self, '_last_rate_limit_warning'):
                self._last_rate_limit_warning = {}
            
            last_warning = self._last_rate_limit_warning.get(window_id, 0)
            if now - last_warning > 5.0:  # Show warning at most every 5 seconds
                sublime.status_message("AutoPlace: Rate limited")
                self._last_rate_limit_warning[window_id] = now
            
            return False
        
        # Add current timestamp
        timestamps.append(now)
        return True
    
    def _determine_target_group(self, view: sublime.View) -> Optional[int]:
        """Determine the target group for a view using centralized rule engine."""
        return self.rule_engine.determine_target_group(view)
    
    def _place_tab(self, view: sublime.View, target_group: Optional[int] = None) -> None:
        """Place a tab in the specified group."""
        window = view.window()
        if not window:
            return

        # Use provided target group or determine it
        if target_group is None:
            target_group = self._determine_target_group(view)
            if target_group is None:
                return

        current_group, current_index = window.get_view_index(view)
        
        # Check if already in correct group
        if current_group == target_group:
            return

        # Check if target group exists
        layout = window.layout()
        num_groups = len(layout.get("cells", []))
        if target_group >= num_groups:
            error_msg = f"Target group {target_group} doesn't exist (only {num_groups} groups)"
            self._user_warning(error_msg)
            return

        # Perform the placement
        try:
            # Use per-view placement locks to prevent race conditions
            view_id = view.id()
            if view_id in self._placement_locks:
                return
            
            self._placement_locks.add(view_id)
            
            try:
                # Move the tab
                window.set_view_index(view, target_group, -1)  # -1 = append to end
                self._debug_print(f"Moved tab to group {target_group}: {view.file_name() or view.name()}")
            finally:
                self._placement_locks.discard(view_id)
                
        except Exception as e:
            self._debug_print(f"Failed to place tab: {e}")
    
    def _normalize_path(self, path: str) -> str:
        """Normalize file path for consistent matching."""
        return os.path.normpath(path).replace("\\", "/")
    
    def _get_effective_settings(self, window: sublime.Window) -> Dict[str, Any]:
        """Get effective settings (global + project overrides)."""
        effective = {}
        
        # Start with global settings
        for key in ["auto_place_on_activation", "auto_place_on_load", "enable_debug_prints", 
                   "exclude_patterns", "max_placements_per_second", "large_file_threshold",
                   "project_file_check_interval", "active_layout", "layouts"]:
            effective[key] = self.settings.get(key)
        
        # Apply project overrides
        project_settings = self._get_project_settings(window)
        effective.update(project_settings)
        
        return effective
    
    def _get_view_sort_key(self, view: sublime.View) -> tuple:
        """Get sort key for view ordering."""
        view_name = view.name() or ""
        file_name = view.file_name()
        
        if file_name:
            return (0, os.path.basename(file_name).lower())
        else:
            return ("", view_name.lower())


# Import command classes from separate module
from .lib.commands import *

# Create event listener instance for Sublime Text to register
event_listener = AutoPlaceEventListener()
