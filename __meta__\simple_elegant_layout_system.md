# Simple & Elegant Layout System

## Overview

The layout system has been completely redesigned to follow the elegant patterns used in `Jorn_SublimeTabOrganizer` and `Jorn_OrganizeViewsByDirectory`. No more complex layout managers, artificial limits, or overengineered solutions.

## How It Works (The Right Way)

### 1. Calculate What's Needed
```python
# Simple: if you need group 7, create 8 groups (0-7)
needed_groups = target_group + 1
```

### 2. Create Layout On-Demand
```python
if layout_type == "columns":
    cols = [i / num_groups for i in range(num_groups + 1)]
    rows = [0.0, 1.0]
    cells = [[i, 0, i + 1, 1] for i in range(num_groups)]
elif layout_type == "rows":
    cols = [0.0, 1.0]
    rows = [i / num_groups for i in range(num_groups + 1)]
    cells = [[0, i, 1, i + 1] for i in range(num_groups)]
elif layout_type == "grid":
    # Smart grid calculation like <PERSON>rn_OrganizeViewsByDirectory
    num_columns = int(math.sqrt(num_groups))
    num_rows = (num_groups + num_columns - 1) // num_columns
    # ... create grid layout
```

### 3. Apply Layout
```python
window.set_layout({"cols": cols, "rows": rows, "cells": cells})
```

That's it. No artificial limits, no complex managers, no overengineering.

## Settings (Simplified)

```json
{
    "auto_adjust_layout": true,
    "missing_group_behavior": "skip",
    "layout_type": "columns",  // "columns", "rows", "grid"
    
    // Optional: Custom layouts for specific group counts
    "layout_configs": {
        "8": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 0.33, 0.66, 1.0],
            "cells": [
                [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
                [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
                [0, 2, 1, 3], [1, 2, 2, 3]
            ]
        }
    }
}
```

## Your Use Case - Fixed

```json
{
    "jorn_auto_place_tabs": {
        "file_type_rules": {
            "0": [".py", ".pyw"],
            "1": [".js", ".ts", ".jsx", ".tsx"],
            "2": [".html", ".css", ".vue"],
            "7": [".md", ".txt", ".json"]  // This now works perfectly
        },
        "auto_adjust_layout": true,
        "layout_type": "grid"  // Creates a nice grid layout for 8 groups
    }
}
```

## What Was Removed

❌ **Removed Complexity**:
- `max_groups` setting (unnecessary and redundant)
- `layout_strategy` (overengineered)
- `named_layouts` (not needed for this use case)
- `LayoutManager` class (200+ lines of unnecessary code)
- Complex layout selection logic
- Artificial limits and constraints

✅ **Kept Simplicity**:
- Direct layout calculation (like existing Jorn plugins)
- Custom layouts for specific group counts (optional)
- Simple layout types: columns, rows, grid
- Clean, readable code

## Code Comparison

### Before (Overengineered)
```python
class LayoutManager:
    def __init__(self, window, plugin):
        self.layouts = self._load_available_layouts()
    
    def apply_layout_for_groups(self, num_groups):
        # Strategy 1: exact match
        # Strategy 2: named layouts
        # Strategy 3: strategy-based generation
        # Strategy 4: fallback
        # ... 200+ lines of complexity
```

### After (Elegant)
```python
def _create_layout_for_groups(self, window, num_groups):
    # Check for custom layout
    custom_layout = self._get_setting("layout_configs", {}).get(str(num_groups))
    if custom_layout:
        self._apply_layout(window, custom_layout)
        return
    
    # Generate based on type
    layout_type = self._get_setting("layout_type", "columns")
    if layout_type == "columns":
        layout = self._create_columns_layout(num_groups)
    # ... simple, direct, elegant
```

## Benefits

### Simplicity
- **No artificial limits**: Create as many groups as needed
- **Direct calculation**: Simple math, no complex logic
- **Clear intent**: Code does exactly what it says

### Consistency
- **Follows Jorn patterns**: Same approach as existing plugins
- **Familiar API**: Uses established layout creation methods
- **Predictable behavior**: No surprising edge cases

### Maintainability
- **Less code**: ~50 lines vs 200+ lines
- **Easier to debug**: Simple, linear logic
- **Easier to extend**: Add new layout types easily

## The Lesson

Sometimes the "elegant" solution is the **simple** one. The existing Jorn plugins already had the right approach:

1. Calculate what you need
2. Create it directly
3. Apply it

No need for complex managers, strategies, or artificial constraints. Just clean, simple, effective code that does exactly what's needed.

Your original issue (group index 7 not working) is now solved with a simple, elegant solution that follows the established patterns of your plugin ecosystem.
