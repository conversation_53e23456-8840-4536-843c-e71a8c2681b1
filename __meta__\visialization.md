```mermaid
sequenceDiagram
    participant User
    participant ST as Sublime Text
    participant Plugin as AutoPlace Plugin
    participant Layout as Layout Manager
    participant <PERSON><PERSON><PERSON> as Semantic Detector

    User->>ST: Open new project folder
    User->>ST: Ctrl+Shift+P → "Create Layout from Template"
    ST->>Plugin: Command: create_layout_from_template
    Plugin->>Layout: Get available templates
    Layout-->>Plugin: [Web Dev, Python, Data Science, etc.]
    Plugin->>User: Show template selection
    User->>Plugin: Select "Web Development"
    Plugin->>ST: Create .sublime-project with layout
    Plugin->>User: "Layout 'web-dev' created and activated"

    Note over User,Semantic: Daily Usage Begins

    User->>ST: Open src/components/Header.vue
    ST->>Plugin: on_load_async(Header.vue)
    Plugin->>Semantic: get_semantic_types(Header.vue)
    Semantic-->>Plugin: [project, exists, saved, has_syntax]
    Plugin->>Layout: Match rules for Header.vue
    Layout-->>Plugin: Group 0 (Source Files)
    Plugin->>ST: Move tab to Group 0
    ST-->>User: Tab appears in Group 0

    User->>ST: Open tests/Header.test.js
    ST->>Plugin: on_load_async(Header.test.js)
    Plugin->>Semantic: get_semantic_types(Header.test.js)
    Semantic-->>Plugin: [project, exists, saved, has_syntax]
    Plugin->>Layout: Match rules for Header.test.js
    Layout-->>Plugin: Group 1 (Test Files)
    Plugin->>ST: Move tab to Group 1
    ST-->>User: Tab appears in Group 1

    User->>ST: Create new untitled file
    ST->>Plugin: on_load_async(untitled)
    Plugin->>Semantic: get_semantic_types(untitled)
    Semantic-->>Plugin: [unsaved, scratch, empty]
    Plugin->>Layout: Match rules for untitled
    Layout-->>Plugin: Group 3 (Temporary)
    Plugin->>ST: Move tab to Group 3
    ST-->>User: Tab appears in Group 3

    User->>ST: Switch to Header.vue tab
    ST->>Plugin: on_activated_async(Header.vue)
    Plugin->>Plugin: Check if already in correct group
    Plugin-->>ST: Already correct, no action

    Note over User,Semantic: Result: Organized workspace
```