{"auto_complete": {"selected_items": [["intens", "Intensity"], ["LVL", "LVL1_INTERPRETATION"], ["de", "default_prompt"], ["def", "default_prompt"], ["origina", "originality"], ["sequ", "sequentialthinking"]]}, "buffers": [{"file": "Jorn_AutoPlaceTabs.py", "settings": {"buffer_size": 13938, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": [[5, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACiAgAAAAAAAKICAAAAAAAAAAAAAAAA8L8"], [10, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACsAwAAAAAAAKwDAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Jorn_AutoPlaceTabs.code-workspace", "settings": {"buffer_size": 102, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": [[3, 1, "toggle_comment", {"block": false}, "AwAAAEYAAAAAAAAASQAAAAAAAAAAAAAALgAAAAAAAAAxAAAAAAAAAAAAAAAqAAAAAAAAAC0AAAAAAAAAAAAAAA", "AQAAAAAAAAABAAAARwAAAAAAAAArAAAAAAAAAAAAAAAAAPC/"]]}, {"contents": "", "file": "Jorn_AutoPlaceTabs.md", "file_size": 0, "file_write_time": 133978367803920272, "settings": {"buffer_size": 0, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[4, 1, "cut", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAKP9AAAAAAAAAAAAAAAA8L8"]]}, {"contents": "{\n    \"jorn_auto_place_tabs\": {\n        # // Enable automatic placement and layout adjustment\n        \"auto_place_on_activation\": true,\n        \"auto_adjust_layout\": true,\n        \"layout_mode\": \"compact\",\n        \"layout_type\": \"grid\",\n        # // SEMANTIC RULE SYSTEM\n        \"group_rules\": {\n            \"0\": [\n                {\n                    \"description\": \"Project Python files\",\n                    \"match\": {\"extensions\": [\".py\", \".pyw\"], \"types\": [\"project\"]},\n                },\n                {\n                    \"description\": \"Project source files in src/lib directories\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/src/*\", \"*/lib/*\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n            ],\n            \"1\": [\n                {\n                    \"description\": \"Project JavaScript/TypeScript files\",\n                    \"match\": {\n                        \"extensions\": [\".js\", \".ts\", \".jsx\", \".tsx\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n                {\n                    \"description\": \"Project test files\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/tests/*\", \"*/test/*\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n            ],\n            \"2\": [\n                {\n                    \"description\": \"Project HTML/CSS/Vue files\",\n                    \"match\": {\n                        \"extensions\": [\".html\", \".css\", \".vue\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n                {\n                    \"description\": \"Project documentation\",\n                    \"match\": {\"directory_patterns\": [\"*/docs/*\"], \"types\": [\"project\"]},\n                },\n                {\"description\": \"External files\", \"match\": {\"types\": [\"external\"]}},\n            ],\n            \"3\": [\n                {\n                    \"description\": \"Project config files\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/config/*\"],\n                        \"types\": [\"project\"],\n                    },\n                }\n            ],\n            \"7\": [\n                {\n                    \"description\": \"Documentation and data files (any location)\",\n                    \"match\": {\"extensions\": [\".md\", \".txt\", \".json\"]},\n                }\n            ],\n        },\n    }\n}\n", "settings": {"buffer_size": 2486, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAFgwAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [3, 1, "auto_indent_generic", {"tab_size": 4}, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAABYMAAAAAAAAAAAAAAAA8L8"], [8, 1, "toggle_comment", {"block": false}, "AQAAAPwAAAAAAAAA/gAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAHAQAAAAAAAAcBAAAAAAAAAAAAAAAA8L8"], [11, 1, "toggle_comment", {"block": false}, "AQAAACgAAAAAAAAAKgAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAyAAAAAAAAADIAAAAAAAAAAAAAAAAA8L8"], [13, 1, "black", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAM4KAAAAAAAAAAAAAAAA8L8"]]}, {"contents": "{\n    \"jorn_auto_place_tabs\": {\n        # // Enable automatic placement and layout adjustment\n        \"auto_place_on_activation\": true,\n        \"auto_adjust_layout\": true,\n        \"layout_mode\": \"compact\",\n        \"layout_type\": \"grid\",\n        # // SEMANTIC RULE SYSTEM\n        \"group_rules\": {\n            \"0\": [\n                {\n                    \"description\": \"Project Python files\",\n                    \"match\": {\"extensions\": [\".py\", \".pyw\"], \"types\": [\"project\"]},\n                },\n                {\n                    \"description\": \"Project source files in src/lib directories\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/src/*\", \"*/lib/*\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n            ],\n            \"1\": [\n                {\n                    \"description\": \"Project JavaScript/TypeScript files\",\n                    \"match\": {\n                        \"extensions\": [\".js\", \".ts\", \".jsx\", \".tsx\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n                {\n                    \"description\": \"Project test files\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/tests/*\", \"*/test/*\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n            ],\n            \"2\": [\n                {\n                    \"description\": \"Project HTML/CSS/Vue files\",\n                    \"match\": {\n                        \"extensions\": [\".html\", \".css\", \".vue\"],\n                        \"types\": [\"project\"],\n                    },\n                },\n                {\n                    \"description\": \"Project documentation\",\n                    \"match\": {\"directory_patterns\": [\"*/docs/*\"], \"types\": [\"project\"]},\n                },\n                {\"description\": \"External files\", \"match\": {\"types\": [\"external\"]}},\n            ],\n            \"3\": [\n                {\n                    \"description\": \"Project config files\",\n                    \"match\": {\n                        \"directory_patterns\": [\"*/config/*\"],\n                        \"types\": [\"project\"],\n                    },\n                }\n            ],\n            \"7\": [\n                {\n                    \"description\": \"Documentation and data files (any location)\",\n                    \"match\": {\"extensions\": [\".md\", \".txt\", \".json\"]},\n                }\n            ],\n        },\n    }\n}\n", "settings": {"buffer_size": 2486, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAtgkAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Main.sublime-menu", "settings": {"buffer_size": 3259, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": [[3, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAABBQAAAAAAAAEFAAAAAAAAAAAAAAAA8L8"]]}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["min", "Jorn - Minify Text"], ["MIN", "Jorn - Minify Text"], ["unm", "Jorn - Unminify Text"], ["un", "Jorn - Unminify Text"], ["i", "Jorn: Time Interval Actions - Run Now"], ["unm ", "Jorn - Unminify Text"], ["orga", "Jorn: Organize Views by Directory"], ["org", "Jorn: Organize Views by Directory"], ["tab", "Table Editor: Enable for current view"], ["prev", "Markdown Preview: Preview in Browser"], ["mi", "Jorn - Minify Text"], ["del comm", "Jorn - Delete Comments"], ["del co", "Jorn - Delete Comments"], ["table syn", "Table Editor: Enable for current syntax"], ["sort r", "Jorn - Sort Lines Chronologically (reverse)"], ["sort rev", "Jorn - Sort Lines Chronologically (reverse)"], ["aaa", "aaa-Jorn: Export <PERSON> to Markdown"], ["load", "Origami: <PERSON><PERSON> Saved Layout"], ["bbb", "bbb-Jorn: Show Tab Overview"], ["table", "Table Editor: Enable for current view"], ["mini", "Jorn - Minify Text"], ["tabl", "Table Editor: Enable for current view"], ["previ", "Markdown Preview: Preview in Browser"], ["aaaa", "aaaa-Jorn: Categorize and Print Tabs (Table Format)"], ["organi", "Jorn: Organize Views by Directory"], ["categ", "Jorn: Categorize and Print Tabs"], ["nubu", "Package Control: User CA bundle"], ["mni", "Jorn - Minify Text"], ["load lay", "Origami: <PERSON><PERSON> Saved Layout"], ["sel com", "Jorn - Select Comments"], ["sort le", "Jorn - Sort Lines by Length"], ["sel du", "Highlight Duplicates: Select Duplicates"], ["preview", "Markdown Preview: Preview in Browser"], ["disab", "Package Control: Disable Package"], ["lengt", "Jorn - Sort Lines by Length"], ["unmini", "Jorn - Unminify Text"], ["sort leng", "Jorn - Sort Lines by Length"], ["autoso", "<PERSON><PERSON> Autosort: Toggle Autosort for Unsaved Tabs"], ["inter", "Jorn: Time Interval Actions - Run Now"], ["inteva", "Jorn: Time Interval Actions - Run Now"], ["del com", "Jorn - Delete Comments"], ["save ", "Origami: Save Current Layout"], ["int", "Jorn: Time Interval Actions - Run Now"], ["interva", "Jorn: Time Interval Actions - Run Now"], ["build view", "Jorn: Build View - Enable/Disable Build View"], ["build v", "Jorn: Build View - Enable/Disable Build View"], ["remove pack", "Package Control: Remove Package"], ["buildv", "Disable/Enable buildview for this window"], ["build vi", "Disable/Enable buildview for this window"], ["install", "Package Control: Install Package"], ["interval", "Jorn: Time Interval Actions - Run Now"], ["insta", "Package Control: Install Package"], ["interv", "Jorn Time Interval Actions: Run Now"], ["disabl", "Package Control: Disable Package"], ["build", "Show/Hide \"Save changes?\" when closing build output view"], ["del", "Jorn - Delete Comments"], ["save la", "Origami: Save Current Layout"], ["log s", "Log Highlight: Generate Syntax & Theme"], ["log", "Set Syntax: Log Highlight"], ["inst", "Package Control: Install Package"], ["inst ", "Install Package Control"]], "width": 0.0}, "console": {"height": 704.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_AutoPlaceTabs"], "file_history": ["/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-settings", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16-e.impactful_quote.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-e.tail_rhyme2.md", "/C/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/__meta__/05.01.2025_conversation.md", "/C/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/__meta__/fra menneske til menneske.txt", "/C/Users/<USER>/Desktop/my/flow/android/prj/prj_perspektiv/__meta__/min_melding_scratchpad.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16-d.alt jeg ikke forstår blir til liv som utspiller seg.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/sub-prompts-notes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_SCRATCHPAD.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16-c.chrome_extension_research-Ubiquitous AI Dominance.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/Prompting.sublime-project", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16.c.chrome_extension_research-Ubiquitous AI Dominance.html", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16.c.chrome_extension_research-Ubiquitous AI Dominance.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.16.b.chrome_extension.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-h.geminiresearch_generalized_prompts.html", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-h.geminiresearch_generalized_prompts.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-g.imagegen_prompts.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-f.critique_enhance.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-e.tail_rhyme3.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__SpeechToText/src/transcriptions/transcriptions.md", "/D/my/archive/MobilBackup/JORN/Lydopptak/m4a/.new_hashes.py", "/D/my/archive/MobilBackup/JORN/Lydopptak/m4a/m4a.dirtree.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.14-a.compare_textfile_similarity.md", "/C/Users/<USER>/Desktop/my/flow/downloads/LLM Integration Dominance Analysis_.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-h.Image Prompt Optimization Enhancement_.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-d.prose_tail_rhymes.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.14-d.tail_rhymes.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.12-d.valid_system_instruction_according_to_system.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.02.13-kl.18.45--src.systems.Py_Ai_Utils.working_systems--llm_framework_py.src.original.ai.instruction_templates.templates.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.15-c.win11_networksharing.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Powershell/win11_enable_network_folder_sharing.ps1", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.12-b.interaktiv_nodeeditor.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.07-kl.15.50--llm_prompting.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.11-.a.simplifying_my_ai_system.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.12-c.combine_instructions.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/exe/LIB/bat/py_venv_terminal.bat", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.14-c.git_add.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.06.17-kl.18.32--Template-Based Instruction Processing.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.13-c.sublime_autonomous_instructions.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.13-b.bursdagskort_generator.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_json/2025.05.24-kl.15.29--ai.src.systems.Py_Ai_Utils.working_systems--g_system_prompting.src.templates.lvl1.templates_lvl1_md_catalog.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/working_systems_py/2025.04.11-kl.22.31--g_system_prompting.src..backups.litellm_sequence_executor.018.py", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/3031/history--2025.07.09-kl.22.09.38--sequence-3031+3100+3049--gpt-4.1.GREAT.GREAT.json", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/working_systems/llm_framework_py/refs/_v/v1/j_templates_backup_1/ai_agent_templates_a1.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/working_systems/llm_framework_py/refs/_v/v1/l_src_backups_2/_md/old/prompt_2025.01.19-e-5.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.04.28 - 12.09 -tmp-schema/categorize_instructions/.specstory/history/2025-04-30_18-38-identifying-successful-instruction-templates.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.06.18-kl.17.54--Instruction Sequence Optimization.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/working_systems/llm_working_singlescripts/e_system_prompting/__meta__/e_chat003.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/__meta__/2025.04.16_c.001-chat.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/__meta__/2025.04.16_b.006-chat.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/__meta__/2025.04.13_002-chat.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.04.13-kl.12.19--src.systems.Py_Ai_Utils.working_systems--g_system_prompting.__meta__.2025.04.13_002-chat.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_ext_repos_md/2024.06.06-kl.00.32--__meta__.ai_ext_repos.ext_repos.0xeb--TheBigPromptLibrary.CustomInstructions.ChatGPT.gAS7SGZTu_The_Unconscious_Character.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.04.18-kl.22.29--_a.001-chat.008.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/__meta__/2025.04.13_005.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/working_systems_md/2025.04.10-kl.15.43--working_systems.f_system_prompting.src.templates.lvl1.lvl1.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_vscode/exe/data/user-data/User/History/3b0b1d8e/SwZP.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.04.16 - 16.28 -/2025.04.16 - 16.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/misc/misc.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/working_systems/llm_framework_py_gui/__meta__/_md/prompt_2025.02.15_c.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.02.18-kl.10.15--src.systems.Py_Ai_Utils.working_systems--llm_framework_py.src.system_instructions._md.perplexity_2025.02.14-a.md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/src/litellm_instruction_runner.promptstodo.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/unsaved_tabs_backup_md/2025.05.16-kl.20.44--unsaved_tabs_backup.ai.2025.05.16-dsk-001-d.kl20__len[609].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.01.20-kl.15.16--Intensity Enhancement Request.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.04.16-kl.10.58--LLM Codebase Familiarization Instructions.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_vscode/exe/data/extensions/saoudrizwan.claude-dev-3.18.14/walkthrough/step5.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/Prompting/2025.07.12-dsk-001-b.kl12__len[44].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.06.13_prompting_codebasepilot.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/__meta__/v2/output/llm.2025.04.16-kl.10.58--LLM Codebase Familiarization Instructions.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.06.08-kl.11.10--Generalized LLM Instruction Templates.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.04.19-kl.22.17--Prompt Engineering Patterns.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ChatGPTExportToMarkdown/out/2025.04.20-kl.17.03--Lineage File Update Plan.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/generalized_system_instructions/2025.04.23_a.001--sysinstructionstemplate.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/generalized_system_instructions/2025.06.19-a.augment_conversation.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/ai/__meta__/ai_ext_repos/ext_repos--awesome-cursorrules2/rules/categorized/Generalized AI Instruction Sequences for .cursorrules Management.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/working_systems/llm_working_singlescripts/__meta__/GeneralizedCodebaseAssimilation.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.07.09-kl.22.14.33--sequence-3031+3100+3049--gpt-4.1.GREAT.json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/2025.04.11-kl.15.34--unsaved_tabs_backup.g_system_prompting.2025.04.11-dsk-004-b.kl15__len[32].json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/2025.07.12-kl.23.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.06.30-kl.13.52.24--sequence-3030+3000--gpt-4.1.GREAT.json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.06.26-kl.19.55.20--sequence-3100-a-c--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.06.19-kl.21.01.35--sequence-sequence-3000--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.06.19-kl.10.38.35--sequence-sequence-3000--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.12-kl.23.01--prompts/history--2025.06.13-kl.22.36.48--sequence-sequence-1203--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/generalized_system_instructions/2025.06.19-scratchpad.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.05.22-kl.09.28--src.systems.Py_Ai_Utils.working_systems--g_system_prompting.src.templates.lvl1.md.0187-h-meta-sequence-self-similarity-check.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.06.13-a.augmentinstructions.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.05.22-kl.09.28--src.systems.Py_Ai_Utils.working_systems--g_system_prompting.src.templates.lvl1.md.0209-c-contextual-sequence-title-synthesis.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_LOG.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_NOTES.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Notes/system_instructions.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.07.04-kl.22.53--gemini.api_web_apps.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.05.22-kl.09.28--src.systems.Py_Ai_Utils.working_systems--g_system_prompting.src.templates.lvl1.md.0066-n-react-techstack-coherence-visualization.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2024.09.19-kl.23.10--src.systems.wip_Py_Ai_Utils.py__GptReasoningChains--win4r.o1..gpt.prompts.openai-chatgpt4o_20240520.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.25-kl.09.27.59--sequence-3100+3000--gpt-4.1.great.json", "/C/Users/<USER>/Desktop/my/BackupRobocopy 8-copy_dir_with_exclusions_2local.bat", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Scripts/Python/Py_Ai_Utils/ai/src/systems/Py_Ai_Utils.working_systems--g_system_prompting/src/templates/lvl1/md/0065-m-react-techstack-coherence-visualization.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/unsaved_tabs_backup_md/2025.04.24-kl.23.19--unsaved_tabs_backup.2025.04.24-dsk-002-d.kl23__len[5790].md", "/D/found.000/dir0000.chk/User/Nas_Flow_Jorn/__GOTO__/Projects/prj_ringerikelandskap/prj/rlweb-000/rl-website_web/__meta__/.specstory/history/2025-02-26_17-01-exploring-and-documenting-tech-stack-for-ringerike-landskap.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.01.27-kl.17.41--src.systems.Py_Ai_Utils.working_systems--llm_framework_py.refs._v.v1.g_md.scratchpad.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.22-dsk-005-d.kl18__len[161].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/unsaved_tabs_backup_md/2025.06.17-kl.18.50--unsaved_tabs_backup.helserelatert.2025.06.17-dsk-002-c.kl18__len[620].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/2025.06.21-prompt_ringerikelandskap_arbeidskontrakt.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/chats/2025.06.24-kl.14.49--chatgpt.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_md/2025.04.14-kl.19.11--src.systems.Py_Ai_Utils.working_systems--g_system_prompting.src.templates.lvl1._md_archived.0078-d-construct-generalized-community-descriptor.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/generalized_system_instructions/chats/2025.06.18-kl.11.15--685198b8-dffc-8008-b258-40291539f68a--generalized_instructions.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_py/2025.01.15-kl.15.08--ai.src.systems.Py_Ai_Utils--simple_llm_instructions.src.ai.models.openai_codegeneralizer.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_vscode/exe/data/user-data/User/workspaceStorage/5e6782f0fbe715993321aa97d785342f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/0dfb2ad6-41c3-490e-a099-5e63c40472ac/document-c__Users_DSK_Desktop_my_flow_home___GOTO___Scripts_Python_Py_Cli_Utils_py__WindowTiler_README_INTERACTIVE.md-1750322556205-43b25ddc-3a6c-45d2-bb0c-4b65bfa333b2.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_vscode/exe/data/user-data/User/workspaceStorage/5e6782f0fbe715993321aa97d785342f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/0dfb2ad6-41c3-490e-a099-5e63c40472ac/document-c__Users_DSK_Desktop_my_flow_home___GOTO___Scripts_Python_Py_Cli_Utils_py__WindowTiler_src_window_tiler.py-1750320260625-bfbb91dd-8ee4-4586-967b-2310d4afd461.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/archive_2024/ai_py/2025.02.07-kl.19.33--ai.src.systems.Py_Ai_Utils.working_systems--llm_framework_py.refs._v.v1.k_2025.02.07-minified_working_versions.template_runner.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/Augment-Memories/memories/r5/2025.06.13.a.memoriesconsolidate.r3.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Prompting/Augment-Memories/memories/r1/Augment-Memeory.023.general.md", "/C/Users/<USER>/Desktop/__SCRATCH__/New folder (2)/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/notes/_generated/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/notes/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/prompts/prompts.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/prompts/2024.10.22-kl.16.28--imageprompts_006.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/prompts/2024.10.19-kl.16.52--imageprompts_005.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/prompts/2024.10.19-kl.21.06--imageprompts_001.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/archive_2024/imageprompts/prompts/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/archive_2024/imageprompts/prompts/19.10.2024_imageprompts_001.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/x/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/generated_images/New folder (2)/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/archive_2024/unsorted_imgs 1/.new_hashes.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/private/archive_2024/unsorted_imgs/.new_hashes.py"], "find": {"height": 29.6}, "find_in_files": {"height": 658.4, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": [], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "Jorn_AutoPlaceTabs.py", "semi_transient": false, "settings": {"buffer_size": 13938, "regions": {}, "selection": [[1244, 1244]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".py", "current_syntax": "Python/Python", "enabled": true, "last_updated": 1753363272.83, "scanning": "-comment"}, "sorttabs_lastactivated": 1753363306.59, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 2594, "tab_activation_time": 1753363306, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Jorn_AutoPlaceTabs.code-workspace", "selected": true, "semi_transient": false, "settings": {"buffer_size": 102, "regions": {}, "selection": [[80, 46]], "settings": {"apply_syntax_touched": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".code-workspace", "current_syntax": "JSON/JSON", "enabled": true, "last_updated": 1753363272.83, "scanning": "-comment"}, "sorttabs_lastactivated": 1753363340.33, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 34, "tab_activation_time": 1753363340, "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "Jorn_AutoPlaceTabs.md", "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"allow_quiet_save": true, "apply_syntax_touched": true, "color_helper.box_height": 16, "color_helper.color_scheme": "<PERSON><PERSON>_<PERSON>_Dark_Focused.sublime-color-scheme", "color_helper.refresh": true, "sorttabs_lastactivated": 1753362819.04, "syntax": "Packages/Markdown/Markdown.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1753362819, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "semi_transient": false, "settings": {"buffer_size": 2486, "regions": {}, "selection": [[0, 2486]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Apps\\app_sublimetext\\exe\\Data\\Packages\\Jorn_AutoPlaceTabs\\__meta__", "sorttabs_lastactivated": 1753360766.13, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1753360766, "tab_size": 4, "translate_tabs_to_spaces": true, "word_wrap": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 5, "stack_multiselect": false, "type": "text"}, {"buffer": 4, "semi_transient": false, "settings": {"buffer_size": 2486, "regions": {}, "selection": [[987, 987]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\my\\flow\\home\\__GOTO__\\Apps\\app_sublimetext\\exe\\Data\\Packages\\Jorn_AutoPlaceTabs\\__meta__", "sorttabs_lastactivated": 1753362073.02, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1283, "tab_activation_time": 1753362073, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 4, "stack_multiselect": false, "type": "text"}, {"buffer": 5, "file": "Main.sublime-menu", "semi_transient": false, "settings": {"buffer_size": 3259, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753362083.79, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 8, "tab_activation_time": 1753362083, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 29.6}, "input": {"height": 243.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.find_results": {"height": 0.0}, "output.project_environment_log": {"height": 0.0}, "pinned_build_system": "[py:build2] \t ai_systems.0001.sublime-project", "project": "Jorn_AutoPlaceTabs.sublime-project", "replace": {"height": 56.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["litellm", "src\\playground\\litellm_instruction_runner.py"], ["runner", "src\\systems\\Py_Ai_Utils.working_systems--llm_framework_py\\src\\original\\ai\\template_runner.py"], ["enerate-cinematic-video-promp", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0222-a-generate-cinematic-video-prompter.md"], ["story--2025.05.18-kl.22.05--sequence-0001-a+0001-b--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.18-kl.22.05--sequence-0001-a+0001-b--gpt-4.1.json"], ["0222", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0222-a-aggregator-combiner.md"], ["instr runner", "src\\systems\\Py_Ai_Utils.working_systems--llm_framework_py\\src\\original\\ai\\template_runner_sysinstr_b.py"], ["istory--2025.05.10-kl.16.40--sequence-0217--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.10-kl.16.40--sequence-0217--gpt-4.1.json"], ["istory--2025.05.08-kl.19.17--sequence-0001-a+0194-a+0001-b+0001-a+0001-b--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.08-kl.19.17--sequence-0001-a+0194-a+0001-b+0001-a+0001-b--gpt-4.1.json"], ["tory--2025.05.07-kl.19.35--sequence-0001-a+0001-b+0001-a+0001-b--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.07-kl.19.35--sequence-0001-a+0001-b+0001-a+0001-b--gpt-4.1.json"], ["tput\\history--2025.05.05-kl.13.01--sequence-0217+0001-a+0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.05-kl.13.01--sequence-0217+0001-a+0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json"], ["kl.13.01--sequence-0217+0001-a+0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.05-kl.13.01--sequence-0217+0001-a+0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json"], ["0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.05-kl.12.50--sequence-0217+0001-a+0001-b+0004-c+0217-a+0001-a+0004-c--gpt-4.1.json"], ["0217", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0217-b-special-intensity-enhancer.md"], ["2025.05.04-kl.19.34--sequence-0217--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.04-kl.19.34--sequence-0217--gpt-4.1.json"], ["0004", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0004-c-intensity-enhancer.md"], ["5.04-kl.18.19--sequence-0001--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.05.04-kl.18.19--sequence-0001--gpt-4.1.json"], ["0090-a-essence-extraction", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0090-a-essence-extraction.md"], ["0090-c-intent-amplification", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0090-c-intent-amplification.md"], ["0087-b-distill-and-clarify", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0087-b-distill-and-clarify.md"], ["0087-a-extract-core-intent", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0087-a-extract-core-intent.md"], ["0062-c-deep-intent-extraction", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0062-c-deep-intent-extraction.md"], ["0056-a-promptoptimizer", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0056-a-promptoptimizer.md"], ["0046-a-convergent-significance-extraction", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0046-a-convergent-significance-extraction.md"], ["0038-d-architectural-intent-illumination", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\_md_archived\\0038-d-architectural-intent-illumination.md"], ["0075-i+0115-g+0161-a+0191-i+0202-b+0202-c+0202-d+0202-f+0203-d+0204-a+0188--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.30-kl.19.29--sequence-0075-i+0115-g+0161-a+0191-i+0202-b+0202-c+0202-d+0202-f+0203-d+0204-a+0188--gpt-4.1.json"], ["0037-e", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0037-e-precision-enhancement.md"], ["-2025.04.30-kl.14.53--sequence-0208--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.30-kl.14.53--sequence-0208--gpt-4.1.json"], ["-sequence-0203-b-d+0197-b-d+0182-self-_--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.30-kl.10.48--sequence-0203-b-d+0197-b-d+0182-self-_--gpt-4.1.json"], ["-self-", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0182-a-self-perception.md"], ["history--2025.04.29-kl.11.30--sequence-0111--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.29-kl.11.30--sequence-0111--gpt-4.1.json"], [".28-kl.18.54--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.28-kl.18.54--sequence-0203--gpt-4.1.json"], ["4.27-kl.21.29--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.21.29--sequence-0203--gpt-4.1.json"], ["quence-0206--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.23--sequence-0206--gpt-4.1.json"], ["4.27-kl.14.24--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.24--sequence-0203--gpt-4.1.json"], ["kl.14.23--sequence-0206--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.23--sequence-0206--gpt-4.1.json"], ["-2025.04.27-kl.14.16--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.16--sequence-0203--gpt-4.1.json"], ["history--2025.04.27-kl.14.13--sequence-0206--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.13--sequence-0206--gpt-4.1.json"], ["tory--2025.04.27-kl.14.03--sequence-0205--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.14.03--sequence-0205--gpt-4.1.json"], ["13.02--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.13.02--sequence-0203--gpt-4.1.json"], ["l.12.48--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.27-kl.12.48--sequence-0203--gpt-4.1.json"], ["2025.04.26_a_codebaseassimilation.00.md", "__meta__\\memorybank\\prompts\\2025.04.26_a_codebaseassimilation.00.md"], ["0202", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0202-b-core-dynamic-abstraction.md"], ["25.04.26-kl.14.39--sequence-0203--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.26-kl.14.39--sequence-0203--gpt-4.1.json"], ["instructionconverter", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0001-a-rephrase-instructionconverter.md"], ["0001", "__meta__\\ai_ext_repos\\ext_repos--awesome-cursorrules2\\rules\\categorized\\0001-a-metadata-inventory-extraction.md"], ["0001-instructionconverter", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0001-instructionconverter.md"], [".04.23-kl.18.05--sequence-0191--gpt-4.1.json", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\output\\history--2025.04.23-kl.18.05--sequence-0191--gpt-4.1.json"], ["RunwayGen3PromptBuilder", "src\\systems\\Py_Ai_Utils.working_systems--llm_framework_py\\src\\original\\ai\\instruction_templates\\xml\\RunwayGen3PromptBuilder.xml"], ["litell", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\litellm_sequence_executor.py"], ["a.000_instructions_runner.py", "src\\systems\\Py_Ai_Utils.working_systems--llm_framework_py\\src\\system_instructions\\a.000_instructions_runner.py"], [".xml int", "src\\systems\\Py_Ai_Utils.working_systems--llm_framework_py\\refs\\_v\\v1\\k_2025.02.08-minified_working_versions\\IntensityEnhancer.xml"], ["2025.04.16_b.003", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\__meta__\\2025.04.16_b.003-chat.md"], ["0100-a-primal-essence-extraction", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0100-a-primal-essence-extraction.md"], ["put_2025.04.18-kl.17.50_catalog-0107_gpt-4.1.json", "output_2025.04.18-kl.17.50_catalog-0107_gpt-4.1.json"], ["2025.04.17_d.004-chat.r3", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\__meta__\\2025.04.17_d.004-chat.r3.md"], ["146-e-cleanup-validation-principle-audit-refinement-l", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0146-e-cleanup-validation-principle-audit-refinement-loop.md"], ["j-axiomatic-vectorization-for-one", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0107-j-axiomatic-vectorization-for-one-line.md"], ["t_20250416_152949_catalog-0099_gpt-4.1.json", "output_20250416_152949_catalog-0099_gpt-4.1.json"], ["0005-d-enhance-persuasi", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0005-d-enhance-persuasion.md"], ["2025.04.15_a.001.md", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\__meta__\\2025.04.15_a.001.md"], ["chat 4.1", "src\\simple\\chatgpt4.1_test.py"], ["0002-e-achieving-self-explanation", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0002-e-achieving-self-explanation.md"], ["0059-a-inventory-and-metadata-extraction", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0059-a-inventory-and-metadata-extraction.md"], ["ntent-amplificati", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0090-c-intent-amplification.md"], ["dentify-community-typ", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\src\\templates\\lvl1\\md\\0069-a-community-identify-community-type.md"], ["2025.04.11", "src\\systems\\Py_Ai_Utils.working_systems--g_system_prompting\\__meta__\\2025.04.11_001.md"], ["", "ext_repos\\Zonos-for-windows\\1、install-uv-qinglong.ps1"], ["1", "ext_repos\\Zonos-for-windows\\1、install-uv-qinglong.ps1"], ["CODE_OF_CONDUCT", "playground\\potpie\\CODE_OF_CONDUCT.md"], ["intensi", "template_runner_sysinstr_d_interactive_optimizer\\outputs_archived\\IntensityEnhancer.xml"], ["cogni", "template_runner_lanchain\\instruction_templates\\xml\\in_string\\out_string\\runway\\CognitiveStyleInfuser.xml"], ["RunwayPromptBui", "template_runner_lanchain\\instruction_templates\\xml\\in_string\\out_string\\runway\\RunwayPromptBuilder.xml"], ["VisualPrompt", "template_runner_lanchain\\instruction_templates\\xml\\in_string\\out_string\\runway\\VisualPromptGenerator_b2.xml"], ["Runwayp", "template_runner_lanchain\\instruction_templates\\xml\\in_string\\out_string\\runway\\RunwayPromptBuilder_b1.xml"], ["meaning", "template_runner_lanchain\\instruction_templates\\xml\\in_string\\out_string\\simplified\\ExtractMeaning.xml"], ["clean r", "template_runner_lanchain\\llm_template_runner_clean.py"], ["complexityreducer_05_medium_good", "agent_candidates\\complexityreducer_05_medium_good.py"], ["great", "agent_candidates\\complexityreducer_04_complex_great.py"], ["url", "_venv\\Lib\\site-packages\\werkzeug\\urls.py"], ["sub prompt", "py__ProjectGenerator\\src\\project\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py"], ["sub-pro", "py__ProjectGenerator\\src\\sublime_project\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py"], ["logger", "py__ProjectGenerator\\core\\logger.py"], ["prompt note", "py__ProjectGenerator\\sublime_project\\templates\\prompts\\unsorted_gpt_prompts\\sub-prompts-notes.py"], ["sub prom", "_GPT\\gpt_prompts\\sub-prompts-notes.py"], ["recent", "NSS\\_2_setup\\_3_menus\\submenus\\mnu_recentpaths.nss"], ["grp_actions_user_git", "NSS\\_2_setup\\_2_items\\grp_actions_user_git.nss"], ["GIT ", "NSS\\_2_setup\\_3_menus\\submenus\\mnu_apps_user_git.nss"], ["SNIPPETS", "NSS\\user\\notes\\nilesoft_shell_snippets.nss"], ["itm_app_user_everything", "NSS\\_2_setup\\_2_items\\apps\\itm_app_user_everything.nss"], ["every", "NSS\\_2_setup\\_3_menus\\mnu_context_everything64.nss"], ["projec", "NSS\\_2_setup\\_3_menus\\submenus\\mnu_user_projects.nss"], ["loss", "NSS\\_2_setup\\_2_items\\apps\\itm_app_user_losslesscut.nss"], ["mnu_apps_user", "NSS\\_2_setup\\_3_menus\\submenus\\mnu_apps_user.nss"], ["lossle", "NSS\\_2_setup\\_2_items\\apps\\itm_app_user_losslesscut.nss"], ["snippets", "NSS\\user\\notes\\nilesoft_shell_snippets.nss"], ["grp_dirs_user_jorn_sync", "NSS\\_2_setup\\_2_items\\grp_dirs_user_jorn_sync.nss"], ["itm_app_user_blender", "NSS\\_2_setup\\_2_items\\apps\\itm_app_user_blender.nss"], ["scratch", "NSS\\_2_setup\\_2_items\\dirs\\itm_dir_user_scratch.nss"]], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": false, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 501.0, "status_bar_visible": true, "template_settings": {}}