# Step 1: Compressed Layout-Centric Settings Structure

## What We've Accomplished ✅

### 1. **Transformed Main Settings File**
- **From**: Rule-centric with verbose examples
- **To**: Compressed layout-centric reference

### 2. **Compressed Structure**

```
Jorn_AutoPlaceTabs.sublime-settings (75 lines total)
├── Global Settings (minimal defaults)
├── Semantic Types Reference (compressed list)
├── Layout Structure Reference (single template)
└── Empty Active Settings (no global layouts)
```

### 3. **Key Features**

#### **Compressed Semantic Types Reference**
```json
"defined_types": {
    // Basic: unsaved, dirty, saved, scratch, readonly
    // Location: project, external
    // Existence: deleted, missing, exists
    // Content: empty, non_empty
    // Activity: active, inactive, visible, background
    // Time: recently_opened, short_lived, inactive_file, stale
    // Size: large_file, small_file
    // Syntax: has_syntax, plain_text
}
```

#### **Single Layout Structure Template**
```json
"layout_structure": {
    "layout_name": {
        "metadata": {"name": "Display name", "description": "Purpose"},
        "layout": {"cols": [...], "rows": [...], "cells": [...]},
        "rules": {
            "0": {
                "name": "Group name",
                "match": {"extensions": [...], "types": [...]},
                "exclude": {"types": [...]}
            }
        },
        "settings": {"thresholds": "..."}
    }
}
```

### 4. **Concise Project Template**
- 35 lines total (vs 82 lines before)
- Shows essential structure only
- Easy to copy and customize

## Benefits Achieved

### 1. **Reference-Driven**
- Main settings file is pure documentation
- All options visible with descriptions
- Easy to discover capabilities

### 2. **Self-Contained Layouts**
- Each layout is complete and portable
- No orphaned rules or layouts
- Easy to share between projects

### 3. **Rich Semantic System**
- 17 different semantic types
- Time and size-based intelligence
- Activity and state awareness

### 4. **Project-Specific**
- No global interference
- Each project optimized for its workflow
- Easy switching between layout modes

## Usage Workflow

### **For Users**:
1. **Browse reference**: Open main settings to see all options
2. **Copy layout**: Choose layout example that fits workflow
3. **Paste to project**: Add to `.sublime-project` file
4. **Customize**: Adjust rules and thresholds for project
5. **Activate**: Set as active layout in `_settings`

### **For Developers** (Next Steps):
1. **Parse new structure**: Update plugin to read layout definitions
2. **Implement semantic detection**: Add expanded type detection
3. **Layout management**: Add switching and application logic
4. **Migration**: Convert old format to new format

## File Changes

### **Modified**:
- `Jorn_AutoPlaceTabs.sublime-settings` - Complete restructure
- `project_template_semantic.sublime-project` - Updated example

### **Created**:
- `step1_layout_centric_settings.md` - This documentation

## Next Steps Preview

**Step 2** will focus on:
- Creating `LayoutDefinition` class
- Implementing `SemanticTypeDetector` 
- Refactoring main plugin class to use layout-centric approach

The foundation is now solid for the layout-centric architecture! 🎯
